<!--index.wxml-->
<!-- 最基础的测试元素 -->
<view style="position: fixed; top: 0; left: 0; width: 100%; height: 100px; background: red; color: white; font-size: 20px; z-index: 10000; display: flex; align-items: center; justify-content: center;">
  页面已加载 - 如果看到这个说明WXML正常
</view>

<view class="container {{bubbleFocused ? 'cursor-pointer' : ''}} {{isDarkMode ? 'dark-mode' : ''}}">

  <!-- 泡泡画布区域 - 在使用泡泡样式时显示 -->
  <canvas
    wx:if="{{interfaceStyle === 'bubble'}}"
    type="2d"
    id="bubble-canvas"
    class="bubble-canvas"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    disable-scroll="true">
  </canvas>

  <!-- 星星画布区域 - 在使用星星样式时显示 -->
  <canvas
    wx:if="{{interfaceStyle === 'star'}}"
    type="2d"
    id="star-canvas"
    class="star-canvas {{isDarkMode ? 'dark-mode' : ''}}"
    bindtouchstart="onTouchStart"
    bindtouchmove="onTouchMove"
    bindtouchend="onTouchEnd"
    disable-scroll="true">
  </canvas>

  <!-- 页面标题 -->
  <view class="title-container">
    <text class="title"></text>
    <text class="subtitle"></text>
  </view>

  <!-- 调试信息 - 临时添加 -->
  <view class="debug-info" style="position: fixed; top: 100px; left: 20px; background: rgba(0,0,0,0.8); color: white; padding: 10px; border-radius: 5px; font-size: 12px; z-index: 9999;">
    <text>调试信息:\n</text>
    <text>初始化状态: {{initialized ? '已完成' : '未完成'}}\n</text>
    <text>加载失败: {{loadingFailed ? '是' : '否'}}\n</text>
    <text>界面样式: {{interfaceStyle}}\n</text>
    <text>进度: {{initProgress}}%</text>
  </view>

  <!-- 加载指示器 -->
  <view class="loading-container {{interfaceStyle === 'star' ? 'dark-loading' : ''}}" wx:if="{{!initialized && !loadingFailed}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中... {{initProgress > 0 ? initProgress + '%' : ''}}</text>
    <!-- 进度条 -->
    <view class="progress-bar-container" wx:if="{{initProgress > 0}}">
      <view class="progress-bar" style="width: {{initProgress}}%"></view>
    </view>
  </view>

  <!-- 加载失败提示 -->
  <view class="loading-container error-container {{interfaceStyle === 'star' ? 'dark-loading' : ''}}" wx:if="{{loadingFailed}}">
    <view class="error-icon">!</view>
    <text class="loading-text error-text">加载失败</text>
    <button class="retry-button" bindtap="retryLoading">点击重试</button>
  </view>

  <!-- 主题详情弹窗 -->
  <view class="theme-modal {{showThemeModal ? 'visible' : ''}}" bindtap="closeThemeModal">
    <view class="theme-modal-content" catchtap="preventBubble">
      <view class="theme-header">
        <view class="theme-title">{{currentTheme.name}}</view>
        <view class="theme-close" bindtap="closeThemeModal">×</view>
      </view>
      <view class="theme-description">
        <text>{{currentTheme.description}}</text>
      </view>
      <button class="theme-button" bindtap="startLearning">开始学习</button>
    </view>
  </view>

  <!-- 新手引导覆盖层 -->
  <view class="guide-overlay {{showGuide ? 'visible' : ''}}" bindtap="closeGuide">
    <view class="guide-content">
      <view class="guide-title">欢迎使用</view>
      <view class="guide-text">欢迎来到沟通实验室！点击彩色{{interfaceStyle === 'bubble' ? '气泡' : '星星'}}，探索AI与人类交互的不同主题。每个{{interfaceStyle === 'bubble' ? '气泡' : '星星'}}代表一个独特领域，拖动{{interfaceStyle === 'bubble' ? '气泡' : '星星'}}可以与之互动。</view>
      <button class="guide-button" bindtap="closeGuide">我知道了</button>
    </view>
  </view>



</view>
